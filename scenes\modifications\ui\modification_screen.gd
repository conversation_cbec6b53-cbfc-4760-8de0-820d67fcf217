extends Control

# 修改界面控制器
# 当玩家按下B键时显示/隐藏，包含5x5网格和底部物品栏

# 信号
signal modification_screen_opened
signal modification_screen_closed

# 界面组件引用
@onready var grid_panel = %GridPanel
@onready var grid_container = %GridContainer
@onready var inventory_panel = %InventoryPanel
@onready var inventory_container = %InventoryContainer
@onready var player_placeholder = %PlayerPlaceholder
@onready var animation_container = $AnimationContainer

# 脚本引用
const ModInventoryItemScript = preload("res://scenes/ui/mod_inventory_item.gd")
const ModGridCellScript = preload("res://scenes/ui/mod_grid_cell.gd")

# 常量定义
const PART_SCALE_FACTOR = 0.7
const PLAYER_SCALE_FACTOR = 0.5
const MODIFICATION_SCREEN_SCALE = 0.85

# 改装件数据
var placeholder_mods = [
	{"name": "测试配件1", "color": Color(0.3, 0.7, 1.0)},
	{"name": "测试配件2", "color": Color(0.3, 0.7, 1.0)},
	{"name": "测试配件3", "color": Color(0.3, 0.7, 1.0)},
	{"name": "测试配件4", "color": Color(0.3, 0.7, 1.0)},
	{"name": "测试配件5", "color": Color(0.3, 0.7, 1.0)}
]

# 占据的网格映射 - 用于追踪哪些格子已被占据
var occupied_cells = {}
# 玩家位置 - 中心位置
var player_position = Vector2(5, 4)  # 11x9网格的中心位置

# 界面状态
var is_open: bool = false
var is_animating: bool = false

# 网格单元格预设
const CELL_SIZE = Vector2(100, 100)  # 从50x50增加到100x100
const CELL_COUNT = 99  # 11x9
const GRID_SIZE = 11  # 列数

# 初始化
func _ready():
	# 初始状态为隐藏
	visible = false
	
	# 缩小到0大小（准备后续动画）
	grid_panel.scale = Vector2.ZERO
	inventory_panel.position.y = 720 + inventory_panel.size.y  # 移到屏幕外
	
	# 应用整体缩放
	scale = Vector2.ONE * MODIFICATION_SCREEN_SCALE
	
	# 初始化网格
	setup_grid()
	
	# 尝试加载已保存的改装状态
	load_modification_state()
	
	# 确保根节点可以接收拖放
	mouse_filter = Control.MOUSE_FILTER_STOP
	
	# 创建一个全屏的透明面板来捕获所有拖放事件
	var panel_bg = ColorRect.new()
	panel_bg.color = Color(0, 0, 0, 0.01)  # 几乎完全透明
	panel_bg.mouse_filter = Control.MOUSE_FILTER_PASS  # 确保鼠标事件传递
	panel_bg.anchors_preset = Control.PRESET_FULL_RECT
	panel_bg.z_index = -100  # 确保在最底层
	add_child(panel_bg)
	
	# 开启拖放处理（确保这个节点可以接收拖放）
	set_process_input(true)

# 设置网格
func setup_grid():
	# 清空现有网格
	for child in grid_container.get_children():
		child.queue_free()
	
	# 清空占据格子映射
	occupied_cells.clear()
	
	# 创建11x9网格
	grid_container.columns = GRID_SIZE  # 设置为11列
	for i in range(CELL_COUNT):
		var cell = Panel.new()
		cell.custom_minimum_size = CELL_SIZE
		cell.size_flags_horizontal = Control.SIZE_FILL
		cell.size_flags_vertical = Control.SIZE_FILL
		
		# 计算行列位置（用于后续处理）
		var row = i / GRID_SIZE
		var col = i % GRID_SIZE
		
		# 应用网格单元格脚本
		cell.set_script(ModGridCellScript)
		cell.grid_position = Vector2(col, row)
		
		# 保存对修改界面的引用，以便调用连通性检查
		cell.modification_screen = self
		
		# 如果是中心单元格，标记为玩家位置
		if row == 4 and col == 5:  # 11x9网格的中心位置
			cell.modulate = Color(0.3, 0.3, 0.3, 0.5)  # 半透明
			# 暂时禁用玩家位置的拖放功能
			cell.mouse_filter = Control.MOUSE_FILTER_IGNORE
			# 标记中心为玩家位置 - 默认已占据
			occupied_cells[Vector2(col, row)] = {"is_player": true}
			
			# 应用玩家模型缩放
			if player_placeholder:
				player_placeholder.scale = Vector2.ONE * PLAYER_SCALE_FACTOR
		
		grid_container.add_child(cell)

# 创建物品栏占位符改装件
func create_inventory_placeholders():
	# 清空现有物品栏
	for child in inventory_container.get_children():
		child.queue_free()
	
	# 收集已经被装备的道具信息
	var equipped_mods = []
	for pos in occupied_cells.keys():
		# 跳过玩家位置
		if occupied_cells[pos].has("is_player") and occupied_cells[pos]["is_player"]:
			continue
		
		# 添加已装备的道具名称到数组
		if occupied_cells[pos].has("name"):
			equipped_mods.append(occupied_cells[pos]["name"])
	
	# 获取改装数据管理器
	var mod_data_manager_script = load("res://scenes/modifications/scripts/modification_data_manager.gd")
	var mod_data_manager = mod_data_manager_script.get_instance()
	
	# 添加占位符改装件（只添加未装备的）
	for i in range(placeholder_mods.size()):
		var mod_data = placeholder_mods[i]
		
		# 检查这个道具是否已经被装备了
		if mod_data["name"] in equipped_mods:
			print("跳过已装备道具: " + mod_data["name"])
			continue
		
		# 获取改装件资源
		var mod_resource = mod_data_manager.get_modification_data(mod_data["name"])
		
		# 创建改装件物品
		var item = Panel.new()
		item.custom_minimum_size = Vector2(70, 70) * PART_SCALE_FACTOR
		item.set_script(ModInventoryItemScript)
		
		# 设置属性
		item.mod_name = mod_data["name"]
		item.mod_color = mod_resource.color
		item.inventory_index = i  # 设置物品栏索引
		
		# 创建视觉内容
		var container = VBoxContainer.new()
		container.alignment = BoxContainer.ALIGNMENT_CENTER
		container.mouse_filter = Control.MOUSE_FILTER_PASS  # 确保鼠标事件传递给父节点
		
		# 图标
		var icon = ColorRect.new()
		icon.color = mod_resource.color
		icon.custom_minimum_size = Vector2(50, 50) * PART_SCALE_FACTOR
		icon.size_flags_horizontal = Control.SIZE_SHRINK_CENTER
		icon.mouse_filter = Control.MOUSE_FILTER_PASS  # 确保鼠标事件传递给父节点
		
		# 标签
		var label = Label.new()
		label.text = mod_resource.name
		label.horizontal_alignment = HORIZONTAL_ALIGNMENT_CENTER
		label.mouse_filter = Control.MOUSE_FILTER_PASS  # 确保鼠标事件传递给父节点
		
		# 组装界面
		container.add_child(icon)
		container.add_child(label)
		item.add_child(container)
		
		# 添加到物品栏
		inventory_container.add_child(item)

# 切换修改界面显示状态
func toggle():
	if is_animating:
		return
		
	if is_open:
		close()
	else:
		open()

# 打开修改界面
func open():
	if is_open or is_animating:
		return
		
	is_animating = true
	
	# 显示界面
	visible = true
	
	# 设置现有网格格子的状态
	update_grid_cells_from_occupied()
	
	# 创建物品栏占位符，会自动跳过已装备的道具
	create_inventory_placeholders()
	
	# 更新网格可放置状态视觉提示
	update_grid_placement_visuals()
	
	# 进入慢动作模式而不是暂停游戏
	Engine.time_scale = 0.1  # 降低游戏速度到10%
	
	# 开始打开动画 - 补偿慢动作影响
	var time_compensation = 1.0 / Engine.time_scale  # 补偿因子
	var initial_delay = 0.3 / time_compensation  # 初始显示延迟
	var scale_duration = 0.6 / time_compensation  # 缩放动画时间 - 加快0.2秒
	
	# 计算屏幕正中央位置
	var viewport_size = get_viewport_rect().size
	var screen_center = Vector2(viewport_size.x / 2, viewport_size.y / 2)
	
	# 设置网格面板初始位置 - 直接放在屏幕正中央
	var grid_size = grid_panel.size
	grid_panel.position = screen_center - grid_size/2
	
	# 确保网格面板的中心点是从中心开始扩展的
	grid_panel.pivot_offset = grid_size/2
	
	# 设置初始缩放 - 从一个很小的点开始
	grid_panel.scale = Vector2(0.05, 0.05)  # 非常小的初始大小
	
	# 准备物品栏起始位置
	inventory_panel.position.y = 720 + inventory_panel.size.y  # 从屏幕外开始
	
	# 创建序列补间
	var sequence = create_tween()
	
	# 先停顿短暂时间
	sequence.tween_interval(initial_delay)
	
	# 然后创建并行动画
	sequence.set_parallel(true)
	
	# 网格从中心点向四周扩展 - 加快速度
	sequence.tween_property(grid_panel, "scale", Vector2(1, 1), scale_duration).set_ease(Tween.EASE_OUT).set_trans(Tween.TRANS_BACK)
	
	# 物品栏从底部滑入 - 同步加快
	sequence.tween_property(inventory_panel, "position:y", 720 - inventory_panel.size.y, scale_duration * 0.2).set_ease(Tween.EASE_OUT).set_trans(Tween.TRANS_BACK)
	
	# 动画完成后更新状态
	sequence.chain().tween_callback(func():
		is_animating = false
		is_open = true
		emit_signal("modification_screen_opened")
	)

# 从已占据的格子数据更新网格格子显示
func update_grid_cells_from_occupied():
	# 清空所有现有网格内容
	for i in range(grid_container.get_child_count()):
		var cell = grid_container.get_child(i)
		if cell.has_method("clear_modification_silent"):
			cell.clear_modification_silent()
	
	# 根据占据状态重新填充网格
	for pos in occupied_cells.keys():
		# 跳过玩家位置
		if occupied_cells[pos].has("is_player") and occupied_cells[pos]["is_player"]:
			continue
			
		# 从已保存数据中恢复改装件显示
		if occupied_cells[pos].has("name") and occupied_cells[pos].has("color"):
			# 找到对应的网格格子
			for i in range(grid_container.get_child_count()):
				var cell = grid_container.get_child(i)
				if cell.grid_position == pos:
					# 放置改装件
					cell.place_modification(occupied_cells[pos]["name"], occupied_cells[pos]["color"])
					break

# 关闭修改界面
func close():
	if !is_open or is_animating:
		return
		
	is_animating = true
	
	# 保存当前改装状态到全局状态（如果需要）
	save_modification_state()
	
	# 计算动画时间补偿
	var time_compensation = 1.0 / Engine.time_scale  # 补偿因子
	var animation_duration = 0.55 / time_compensation   # 动画时间 - 减慢0.2秒
	
	# 获取玩家位置 - 改进定位方式
	var player_node = get_tree().get_first_node_in_group("player")
	var target_pos = grid_panel.position  # 默认不移动

	if player_node:
		# 获取玩家的全局变换
		var canvas = get_canvas_transform()
		var global_pos = player_node.global_position
		
		# 转换为屏幕坐标并调整偏移，确保准确定位到玩家中心
		target_pos = global_pos
		
		# 打印调试信息，帮助排查位置问题
		print("玩家位置: ", global_pos)
		print("目标位置: ", target_pos)
		
		# 设置缩小后的大小以匹配玩家大小
		var player_size = 30  # 估计玩家尺寸
		var final_scale = player_size / grid_panel.size.x
		
		# 创建并行补间 - 同时缩小和移动
		var tween = create_tween().set_parallel()
		
		# 先移动面板的枢轴点到左上角，以便能正确缩放到玩家位置
		grid_panel.pivot_offset = Vector2.ZERO
		
		# 调整目标位置，考虑面板大小
		var offset_x = grid_panel.size.x * final_scale / 2
		var offset_y = grid_panel.size.y * final_scale / 2
		target_pos = target_pos - Vector2(offset_x, offset_y)
		
		# 同时缩小并移动到玩家位置 - 减慢速度
		tween.tween_property(grid_panel, "scale", Vector2(final_scale, final_scale), animation_duration).set_ease(Tween.EASE_IN).set_trans(Tween.TRANS_BACK)
		tween.tween_property(grid_panel, "position", target_pos, animation_duration).set_ease(Tween.EASE_IN).set_trans(Tween.TRANS_QUINT)
		
		# 物品栏向底部滑出 - 保持相同比例
		tween.tween_property(inventory_panel, "position:y", 720 + inventory_panel.size.y, animation_duration * 0.5).set_ease(Tween.EASE_IN).set_trans(Tween.TRANS_BACK)
		
		# 动画完成后更新状态
		tween.chain().tween_callback(func():
			is_animating = false
			is_open = false
			visible = false
			
			# 恢复正常游戏速度
			Engine.time_scale = 1.0
			
			# 确保发送信号，让玩家更新改装状态
			emit_signal("modification_screen_closed")
		)
	else:
		# 如果找不到玩家，仍然执行关闭动画
		var tween = create_tween().set_parallel()
		
		# 简单缩小到中心点 - 减慢速度
		tween.tween_property(grid_panel, "scale", Vector2(0.05, 0.05), animation_duration).set_ease(Tween.EASE_IN).set_trans(Tween.TRANS_BACK)
		
		# 物品栏向底部快速滑出
		tween.tween_property(inventory_panel, "position:y", 720 + inventory_panel.size.y, animation_duration * 0.5).set_ease(Tween.EASE_IN).set_trans(Tween.TRANS_BACK)
		
		# 动画完成后更新状态
		tween.chain().tween_callback(func():
			is_animating = false
			is_open = false
			visible = false
			
			# 恢复正常游戏速度
			Engine.time_scale = 1.0
			
			# 确保发送信号，让玩家更新改装状态
			emit_signal("modification_screen_closed")
		)

# 保存当前改装状态到全局状态
func save_modification_state():
	# 检查是否有全局游戏状态管理器
	var game_state = get_tree().get_first_node_in_group("game_state")
	if game_state and game_state.has_method("save_modification_state"):
		# 准备一个可序列化的改装状态
		var serializable_state = {}
		for pos in occupied_cells.keys():
			# 跳过玩家位置
			if occupied_cells[pos].has("is_player") and occupied_cells[pos]["is_player"]:
				continue
				
			# 只保存名称和颜色信息
			if occupied_cells[pos].has("name") and occupied_cells[pos].has("color"):
				# 将Vector2键转换为字符串，方便序列化
				var pos_key = str(pos.x) + "," + str(pos.y)
				serializable_state[pos_key] = {
					"name": occupied_cells[pos]["name"],
					"color": {
						"r": occupied_cells[pos]["color"].r,
						"g": occupied_cells[pos]["color"].g,
						"b": occupied_cells[pos]["color"].b,
						"a": occupied_cells[pos]["color"].a
					}
				}
		
		# 保存到全局状态
		game_state.save_modification_state(serializable_state)
		print("已保存改装状态")
	else:
		print("未找到全局状态管理器，改装状态仅保存在内存中")

# 从全局状态加载改装状态
func load_modification_state():
	# 检查是否有全局游戏状态管理器
	var game_state = get_tree().get_first_node_in_group("game_state")
	if game_state and game_state.has_method("load_modification_state"):
		var loaded_state = game_state.load_modification_state()
		if loaded_state != null:
			# 清除现有状态（除了玩家位置）
			var player_pos_data = null
			if occupied_cells.has(player_position):
				player_pos_data = occupied_cells[player_position]
			
			occupied_cells.clear()
			
			# 恢复玩家位置
			if player_pos_data != null:
				occupied_cells[player_position] = player_pos_data
			
			# 加载保存的状态
			for pos_key in loaded_state.keys():
				# 将字符串键转回Vector2
				var coords = pos_key.split(",")
				var pos = Vector2(int(coords[0]), int(coords[1]))
				
				var mod_data = loaded_state[pos_key]
				var color = Color(
					mod_data["color"]["r"],
					mod_data["color"]["g"],
					mod_data["color"]["b"],
					mod_data["color"]["a"]
				)
				
				occupied_cells[pos] = {
					"name": mod_data["name"],
					"color": color
				}
			
			print("已从全局状态加载改装状态")
			
			# 更新网格显示
			update_grid_cells_from_occupied()
		else:
			print("没有找到保存的改装状态")
	else:
		print("未找到全局状态管理器，无法加载改装状态")

# 获取界面开启状态
func is_modification_screen_open() -> bool:
	return is_open 

# 获取当前装备的改装件
func get_equipped_modifications():
	# 返回当前占据的格子信息，但将Vector2键转换为字符串格式
	var result = {}
	for pos_key in occupied_cells.keys():
		# 将Vector2键转换为字符串格式
		var str_key = str(pos_key.x) + "," + str(pos_key.y)
		result[str_key] = occupied_cells[pos_key]
	return result

# 添加一个辅助函数，获取屏幕坐标
func get_viewport_position(global_position: Vector2) -> Vector2:
	var canvas = get_canvas_transform()
	var viewport_pos = canvas * global_position
	return viewport_pos

# 检查是否可以在指定位置放置改装件
func can_place_at_position(position: Vector2) -> bool:
	# 如果是玩家位置，不能放置
	if position == player_position:
		return false
	
	# 检查是否已经被占据
	if occupied_cells.has(position):
		return false
	
	# 检查是否与玩家直接相邻
	if is_adjacent_to_player(position):
		return true
	
	# 检查是否通过其他改装件与玩家相连
	return is_connected_to_player(position)

# 检查是否与玩家直接相邻
func is_adjacent_to_player(position: Vector2) -> bool:
	# 上下左右四个方向
	var directions = [
		Vector2(0, -1),  # 上
		Vector2(1, 0),   # 右
		Vector2(0, 1),   # 下
		Vector2(-1, 0)   # 左
	]
	
	# 检查四个方向
	for dir in directions:
		var neighbor = position + dir
		if neighbor == player_position:
			return true
	
	return false

# 检查是否通过其他改装件与玩家相连
func is_connected_to_player(position: Vector2) -> bool:
	# 广度优先搜索找到是否有路径连接到玩家
	var visited = {}
	var queue = []
	
	# 添加相邻的已占据格子作为起点
	var directions = [
		Vector2(0, -1),  # 上
		Vector2(1, 0),   # 右
		Vector2(0, 1),   # 下
		Vector2(-1, 0)   # 左
	]
	
	# 首先检查是否直接与玩家相邻
	for dir in directions:
		var neighbor = position + dir
		if neighbor == player_position:
			return true
	
	# 然后检查是否通过其他模块连接到玩家
	for dir in directions:
		var neighbor = position + dir
		if occupied_cells.has(neighbor):
			queue.append(neighbor)
			visited[neighbor] = true
	
	# 如果没有相邻的已占据格子，直接返回false
	if queue.size() == 0:
		return false
	
	# 广度优先搜索
	while queue.size() > 0:
		var current = queue.pop_front()
		
		# 如果找到玩家，返回true
		if current == player_position:
			return true
		
		# 检查相邻格子
		for dir in directions:
			var neighbor = current + dir
			
			# 如果已访问过或不在占据列表中，跳过
			if visited.has(neighbor) or not occupied_cells.has(neighbor):
				continue
			
			# 标记为已访问并添加到队列
			visited[neighbor] = true
			queue.append(neighbor)
	
	# 没有找到路径到玩家
	return false

# 记录格子占据状态
func mark_cell_occupied(position: Vector2, data):
	occupied_cells[position] = data

# 清除格子占据状态
func clear_cell_occupied(position: Vector2):
	if occupied_cells.has(position):
		occupied_cells.erase(position)

# 获取所有有效放置位置
func get_valid_placement_positions() -> Array:
	var valid_positions = []
	
	# 遍历所有格子
	for i in range(GRID_SIZE):
		for j in range(GRID_SIZE):
			var pos = Vector2(i, j)
			if can_place_at_position(pos):
				valid_positions.append(pos)
	
	return valid_positions

# 更新网格可放置状态视觉显示
func update_grid_placement_visuals():
	# 遍历所有网格单元格
	for i in range(grid_container.get_child_count()):
		var cell = grid_container.get_child(i)
		if cell.has_method("update_placement_status"):
			cell.update_placement_status()

# 检查如果移除一个位置的配件，会导致哪些配件失去连接
func get_disconnected_mods_if_removed(position: Vector2) -> Array:
	# 保存当前占据状态的副本
	var original_occupied = occupied_cells.duplicate(true)
	var disconnected_positions = []
	
	# 暂时移除这个位置
	if occupied_cells.has(position):
		occupied_cells.erase(position)
	
	# 检查所有当前占据的非玩家格子
	for pos in occupied_cells.keys():
		# 跳过玩家位置
		if occupied_cells[pos].has("is_player") and occupied_cells[pos]["is_player"]:
			continue
		
		# 检查是否仍然连接到玩家
		if not is_connected_to_player_from_position(pos):
			disconnected_positions.append(pos)
	
	# 恢复原始占据状态
	occupied_cells = original_occupied
	
	return disconnected_positions

# 检查特定位置是否连接到玩家（对现有is_connected_to_player的修改版本）
func is_connected_to_player_from_position(position: Vector2) -> bool:
	# 直接检查是否与玩家相邻
	if is_adjacent_to_player(position):
		return true
		
	# 广度优先搜索找到是否有路径连接到玩家
	var visited = {}
	var queue = []
	
	# 将当前位置添加到队列
	queue.append(position)
	visited[position] = true
	
	# 方向向量
	var directions = [
		Vector2(0, -1),  # 上
		Vector2(1, 0),   # 右
		Vector2(0, 1),   # 下
		Vector2(-1, 0)   # 左
	]
	
	# 广度优先搜索
	while queue.size() > 0:
		var current = queue.pop_front()
		
		# 检查相邻格子
		for dir in directions:
			var neighbor = current + dir
			
			# 如果是玩家位置，返回true
			if neighbor == player_position:
				return true
			
			# 如果已访问过或不在占据列表中，跳过
			if visited.has(neighbor) or not occupied_cells.has(neighbor):
				continue
			
			# 标记为已访问并添加到队列
			visited[neighbor] = true
			queue.append(neighbor)
	
	# 没有找到路径到玩家
	return false

# 收集失去连接的改装配件并返回到物品栏
func collect_disconnected_mods(disconnected_positions: Array) -> Array:
	var collected_mods = []
	
	# 收集所有失去连接的改装配件信息
	for pos in disconnected_positions:
		if occupied_cells.has(pos):
			var mod_data = occupied_cells[pos]
			collected_mods.append({
				"name": mod_data["name"],
				"color": mod_data["color"],
				"position": pos
			})
			
			# 从占据状态中移除
			occupied_cells.erase(pos)
	
	# 找到所有网格单元格并清除这些位置的改装配件
	for i in range(grid_container.get_child_count()):
		var cell = grid_container.get_child(i)
		for pos in disconnected_positions:
			if cell.grid_position == pos:
				cell.clear_modification_silent()  # 不更新全局占据状态
	
	# 更新网格可放置状态
	update_grid_placement_visuals()
	
	return collected_mods

# 将改装配件添加回物品栏
func add_mods_to_inventory(mods: Array):
	for mod_data in mods:
		# 在物品栏中创建新的改装配件项
		var item = Panel.new()
		item.custom_minimum_size = Vector2(70, 70) * PART_SCALE_FACTOR
		item.set_script(ModInventoryItemScript)
		
		# 设置属性
		item.mod_name = mod_data["name"]
		item.mod_color = mod_data["color"]
		# 不需要特定索引，会自动排序到物品栏末尾
		
		# 创建视觉内容
		var container = VBoxContainer.new()
		container.alignment = BoxContainer.ALIGNMENT_CENTER
		container.mouse_filter = Control.MOUSE_FILTER_PASS
		
		# 图标
		var icon = ColorRect.new()
		icon.color = mod_data["color"]
		icon.custom_minimum_size = Vector2(50, 50) * PART_SCALE_FACTOR
		icon.size_flags_horizontal = Control.SIZE_SHRINK_CENTER
		icon.mouse_filter = Control.MOUSE_FILTER_PASS
		
		# 标签
		var label = Label.new()
		label.text = mod_data["name"]
		label.horizontal_alignment = HORIZONTAL_ALIGNMENT_CENTER
		label.mouse_filter = Control.MOUSE_FILTER_PASS
		
		# 组装界面
		container.add_child(icon)
		container.add_child(label)
		item.add_child(container)
		
		# 添加到物品栏
		inventory_container.add_child(item)

# 检查拖动操作，处理连接断开的情况
func handle_mod_drag(from_position: Vector2, keep_dragged_mod: bool = false):
	# 获取如果移除这个位置会导致哪些配件失去连接
	var disconnected_positions = get_disconnected_mods_if_removed(from_position)
	
	# 如果需要保留被拖拽的道具，确保from_position不在disconnected_positions中
	if keep_dragged_mod and disconnected_positions.has(from_position):
		disconnected_positions.erase(from_position)
	
	# 保存当前被拖拽道具的信息，以便后续添加到物品栏
	var dragged_mod_data = null
	if occupied_cells.has(from_position):
		dragged_mod_data = occupied_cells[from_position].duplicate()
		dragged_mod_data["position"] = from_position
	
	if disconnected_positions.size() > 0:
		print("拖走此配件会导致 " + str(disconnected_positions.size()) + " 个配件失去连接")
		
		# 收集失去连接的改装配件
		var collected_mods = collect_disconnected_mods(disconnected_positions)
		
		# 将这些改装配件添加回物品栏
		add_mods_to_inventory(collected_mods)
	
	# 无论是否有断开连接的配件，都清除被拖拽的配件
	if keep_dragged_mod and dragged_mod_data != null:
		# 找到并清除被拖拽格子的内容
		for i in range(grid_container.get_child_count()):
			var cell = grid_container.get_child(i)
			if cell.grid_position == from_position:
				cell.clear_modification_silent()
				# 清除占据状态
				clear_cell_occupied(from_position)
				print("清除格子 " + str(from_position) + " 的配件: [" + dragged_mod_data["name"] + "]")
				break
	
	return disconnected_positions.size() > 0

# 将拖拽中的道具添加回物品栏
func add_dragged_mod_to_inventory(mod_name: String, mod_color: Color):
	# 在物品栏中创建新的改装配件项
	var item = Panel.new()
	item.custom_minimum_size = Vector2(70, 70) * PART_SCALE_FACTOR
	item.set_script(ModInventoryItemScript)
	
	# 设置属性
	item.mod_name = mod_name
	item.mod_color = mod_color
	
	# 创建视觉内容
	var container = VBoxContainer.new()
	container.alignment = BoxContainer.ALIGNMENT_CENTER
	container.mouse_filter = Control.MOUSE_FILTER_PASS
	
	# 图标
	var icon = ColorRect.new()
	icon.color = mod_color
	icon.custom_minimum_size = Vector2(50, 50) * PART_SCALE_FACTOR
	icon.size_flags_horizontal = Control.SIZE_SHRINK_CENTER
	icon.mouse_filter = Control.MOUSE_FILTER_PASS
	
	# 标签
	var label = Label.new()
	label.text = mod_name
	label.horizontal_alignment = HORIZONTAL_ALIGNMENT_CENTER
	label.mouse_filter = Control.MOUSE_FILTER_PASS
	
	# 组装界面
	container.add_child(icon)
	container.add_child(label)
	item.add_child(container)
	
	# 添加到物品栏
	inventory_container.add_child(item)
	print("已将拖拽中的道具 [" + mod_name + "] 添加回物品栏")

# 捕获未能放置在有效网格的拖拽道具
func _can_drop_data(_position, data):
	# 只要是改装件数据都接受，确保任何地方松开鼠标都能处理
	if data is Dictionary and data.has("type") and data["type"] == "modification_part_placeholder":
		print("拖拽道具可以放置在此区域")
		return true
	return false

# 处理拖放数据
func _drop_data(_position, data):
	# 任何拖拽的改装件数据，如果没有放在有效位置，立即回收到物品栏
	if data is Dictionary and data.has("type") and data["type"] == "modification_part_placeholder":
		if data.has("name") and data.has("color"):
			# 检查这个道具是否已经被放置在网格中的其他位置
			var already_placed = false
			for i in range(grid_container.get_child_count()):
				var cell = grid_container.get_child(i)
				if cell.current_mod != null and cell.current_mod["name"] == data["name"] and cell.current_mod["color"] == data["color"]:
					already_placed = true
					break
			
			# 只有当道具未被放置时才添加到物品栏
			if not already_placed:
				add_dragged_mod_to_inventory(data["name"], data["color"])
				print("已将拖拽道具回收到物品栏: [" + data["name"] + "]")

# 处理输入事件，确保能捕获到拖拽释放
func _input(event):
	# 只在界面开启时处理
	if not is_open:
		return
		
	# 检测鼠标释放事件
	if event is InputEventMouseButton:
		var mouse_event = event as InputEventMouseButton
		# 检测左键释放，可能是拖拽结束
		if mouse_event.button_index == MOUSE_BUTTON_LEFT and not mouse_event.pressed:
			# 延迟一帧检查是否有未被接收的拖拽物品
			await get_tree().process_frame
			
			# 检查是否有全局拖拽跟踪器
			if get_tree().root.has_node("GlobalDragTracker"):
				var tracker = get_tree().root.get_node("GlobalDragTracker")
				var drag_data = tracker.get_meta("drag_data") if tracker.has_meta("drag_data") else null
				
				if drag_data != null and drag_data.has("name") and drag_data.has("color"):
					# 检查这个道具是否已经被放置在网格中
					var already_placed = false
					for i in range(grid_container.get_child_count()):
						var cell = grid_container.get_child(i)
						if cell.current_mod != null and cell.current_mod["name"] == drag_data["name"] and cell.current_mod["color"] == drag_data["color"]:
							already_placed = true
							break
					
					# 如果未被放置，添加到物品栏
					if not already_placed:
						add_dragged_mod_to_inventory(drag_data["name"], drag_data["color"])
						print("全局监测：已将未放置的拖拽道具添加回物品栏")
				
				# 清理全局跟踪器
				tracker.queue_free()
