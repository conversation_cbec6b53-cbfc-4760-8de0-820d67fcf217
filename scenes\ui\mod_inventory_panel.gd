extends Control

class_name ModInventoryPanel

# 改装件物品栏面板
# 提供对改装件的筛选、排序和显示功能

# 信号
signal mod_item_selected(mod_resource)

# 组件引用
@onready var mod_inventory_ui = $MarginContainer/VBoxContainer/ModInventoryUI
@onready var filter_size_all = %FilterSizeAll
@onready var filter_size_small = %FilterSizeSmall
@onready var filter_size_medium = %FilterSizeMedium
@onready var filter_size_large = %FilterSizeLarge

@onready var filter_type_all = %FilterTypeAll
@onready var filter_type_attack = %FilterTypeAttack
@onready var filter_type_defense = %FilterTypeDefense
@onready var filter_type_special = %FilterTypeSpecial
@onready var filter_type_core = %FilterTypeCore

# 主题资源
var ui_theme = preload("res://themes/mod_ui_theme.tres")

# 脚本引用
const ModInventoryItemScript = preload("res://scenes/ui/mod_inventory_item.gd")

# 数据引用
var modification_manager = null
var modification_inventory = null

# 过滤器状态
var current_size_filter = "all" # all, small, medium, large
var current_type_filter = "all" # all, offensive, defensive, special, core
var current_search_text = ""

# 常量
const SIZE_CATEGORIES = {
	"small": "小型",
	"medium": "中型",
	"large": "大型"
}

const TYPE_CATEGORIES = {
	"offensive": "攻击型",
	"defensive": "防御型",
	"special": "特殊型",
	"core": "核心型"
}

# 道具尺寸缩放系数
const PART_SCALE_FACTOR = 0.9

# 准备
func _ready():
	print("ModInventoryPanel: 初始化开始")
	
	# 连接子组件的信号
	if mod_inventory_ui:
		mod_inventory_ui.mod_item_selected.connect(_on_mod_selected)
		print("ModInventoryPanel: 已连接子组件信号")
	else:
		push_error("ModInventoryPanel: 无法找到子组件 ModInventoryUI，尝试通过场景树查找")
		# 尝试在整个场景树中查找
		mod_inventory_ui = find_child("ModInventoryUI", true)
		if mod_inventory_ui:
			mod_inventory_ui.mod_item_selected.connect(_on_mod_selected)
			print("ModInventoryPanel: 已通过场景树查找并连接子组件信号")
		else:
			push_error("ModInventoryPanel: 无法找到子组件 ModInventoryUI，某些功能可能无法正常工作")
	
	# 设置筛选按钮
	_setup_filter_buttons()
	
	# 应用主题
	_apply_theme()
	
	print("ModInventoryPanel: 初始化完成")

# 应用主题到所有子组件
func _apply_theme():
	# 为自身设置主题
	theme = ui_theme
	
	# 递归为所有子节点设置主题
	_apply_theme_to_children(self)
	
	print("ModInventoryPanel: 已应用UI主题")

# 递归为子节点设置主题
func _apply_theme_to_children(node):
	for child in node.get_children():
		# 特殊情况: 对特定控件设置特定字体大小
		if child is Label:
			# 根据不同标签设置不同字体大小
			if "Title" in child.name:
				child.add_theme_font_size_override("font_size", 48)
			elif "Filter" in child.get_parent().name:
				child.add_theme_font_size_override("font_size", 40)
		elif child is Button:
			child.add_theme_font_size_override("font_size", 36)
		
		# 递归处理子节点
		if child.get_child_count() > 0:
			_apply_theme_to_children(child)

# 改装件被选中的回调
func _on_mod_selected(mod_resource):
	emit_signal("mod_item_selected", mod_resource)
	print("ModInventoryPanel: 已选中改装件 " + mod_resource.name)

# 刷新物品栏内容
func refresh():
	if mod_inventory_ui and mod_inventory_ui.has_method("refresh"):
		mod_inventory_ui.refresh()
		print("ModInventoryPanel: 已刷新物品栏内容")
	else:
		push_error("ModInventoryPanel: 无法刷新物品栏，子组件不可用")

# 设置筛选按钮
func _setup_filter_buttons():
	# 尺寸筛选按钮
	filter_size_all.pressed.connect(func(): _set_size_filter("all"))
	filter_size_small.pressed.connect(func(): _set_size_filter("small"))
	filter_size_medium.pressed.connect(func(): _set_size_filter("medium"))
	filter_size_large.pressed.connect(func(): _set_size_filter("large"))
	
	# 类型筛选按钮
	filter_type_all.pressed.connect(func(): _set_type_filter("all"))
	filter_type_attack.pressed.connect(func(): _set_type_filter("attack"))
	filter_type_defense.pressed.connect(func(): _set_type_filter("defense"))
	filter_type_special.pressed.connect(func(): _set_type_filter("special"))
	filter_type_core.pressed.connect(func(): _set_type_filter("core"))
	
	# 设置初始状态
	filter_size_all.button_pressed = true
	filter_type_all.button_pressed = true
	
	# 确保每组按钮只能有一个被选中
	var size_group = ButtonGroup.new()
	filter_size_all.button_group = size_group
	filter_size_small.button_group = size_group
	filter_size_medium.button_group = size_group
	filter_size_large.button_group = size_group
	
	var type_group = ButtonGroup.new()
	filter_type_all.button_group = type_group
	filter_type_attack.button_group = type_group
	filter_type_defense.button_group = type_group
	filter_type_special.button_group = type_group
	filter_type_core.button_group = type_group
	
	print("ModInventoryPanel: 筛选按钮已设置")

# 设置尺寸筛选
func _set_size_filter(size_filter: String):
	if mod_inventory_ui and mod_inventory_ui.has_method("set_size_filter"):
		mod_inventory_ui.set_size_filter(size_filter)
		print("ModInventoryPanel: 尺寸筛选已设置为 " + size_filter)
	else:
		push_error("ModInventoryPanel: 无法设置尺寸筛选，子组件不可用")

# 设置类型筛选
func _set_type_filter(type_filter: String):
	if mod_inventory_ui and mod_inventory_ui.has_method("set_type_filter"):
		mod_inventory_ui.set_type_filter(type_filter)
		print("ModInventoryPanel: 类型筛选已设置为 " + type_filter)
	else:
		push_error("ModInventoryPanel: 无法设置类型筛选，子组件不可用")

# 加载管理器
func _load_managers():
	# 加载修改管理器
	if not modification_manager:
		var mod_manager_script = load("res://scenes/modifications/data/modification_data_manager.gd")
		modification_manager = mod_manager_script.get_instance()
	
	# 加载修改库存
	if not modification_inventory:
		var mod_inventory_script = load("res://scenes/modifications/data/mod_inventory.gd")
		modification_inventory = mod_inventory_script.get_instance()
	
	# 刷新物品显示
	refresh_items()

# 刷新物品显示
func refresh_items():
	# 清空现有物品容器
	for child in mod_inventory_ui.get_children():
		child.queue_free()
	
	# 确保已加载管理器
	if not modification_manager or not modification_inventory:
		print("警告: 管理器未加载，无法刷新物品")
		return
	
	# 获取玩家当前拥有的所有改装件
	var player_modifications = modification_inventory.get_all_modifications()
	if player_modifications.size() == 0:
		# 如果没有任何改装件，加载一些测试数据
		_load_test_data()
		player_modifications = modification_inventory.get_all_modifications()
	
	# 获取已装备的改装件ID
	var equipped_mods = _get_equipped_modifications()
	
	# 过滤和排序改装件
	var filtered_mods = []
	for mod_id in player_modifications:
		# 获取配件数据
		var mod_data = modification_manager.get_modification_data(mod_id)
		
		# 如果已经装备了，跳过
		if equipped_mods.has(mod_id):
			continue
		
		# 应用尺寸过滤器
		if current_size_filter != "all":
			var size_category = _get_size_category(mod_data)
			if size_category != current_size_filter:
				continue
		
		# 应用类型过滤器
		if current_type_filter != "all":
			var type_category = _get_type_category(mod_data)
			if type_category != current_type_filter:
				continue
		
		# 应用搜索过滤器
		if current_search_text != "":
			if not mod_data.name.to_lower().contains(current_search_text.to_lower()):
				continue
		
		# 通过所有过滤器，添加到列表
		filtered_mods.append(mod_data)
	
	# 排序（默认按名称）
	filtered_mods.sort_custom(Callable(self, "_sort_by_name"))
	
	# 创建物品UI
	for mod_data in filtered_mods:
		_create_item_ui(mod_data)
	
	print("已刷新物品栏，显示 " + str(filtered_mods.size()) + " 个符合条件的改装件")

# 创建单个改装件的UI
func _create_item_ui(mod_data):
	# 创建面板
	var item = Panel.new()
	item.custom_minimum_size = Vector2(120, 120) * PART_SCALE_FACTOR
	item.set_script(ModInventoryItemScript)
	
	# 设置属性
	item.mod_name = mod_data.id
	item.mod_color = mod_data.color
	
	# 创建视觉内容容器
	var container = VBoxContainer.new()
	container.alignment = BoxContainer.ALIGNMENT_CENTER
	container.mouse_filter = Control.MOUSE_FILTER_PASS
	
	# 创建图标容器，用于布局
	var icon_container = CenterContainer.new()
	icon_container.mouse_filter = Control.MOUSE_FILTER_PASS
	
	# 创建图标
	var icon = ColorRect.new()
	icon.color = mod_data.color
	icon.custom_minimum_size = Vector2(80, 80) * PART_SCALE_FACTOR
	icon.mouse_filter = Control.MOUSE_FILTER_PASS
	
	# 添加图标
	icon_container.add_child(icon)
	
	# 创建信息容器
	var info_container = VBoxContainer.new()
	info_container.mouse_filter = Control.MOUSE_FILTER_PASS
	
	# 名称标签
	var name_label = Label.new()
	name_label.text = mod_data.name
	name_label.horizontal_alignment = HORIZONTAL_ALIGNMENT_CENTER
	name_label.mouse_filter = Control.MOUSE_FILTER_PASS
	name_label.add_theme_font_size_override("font_size", 16)
	
	# 详细信息标签
	var info_label = Label.new()
	info_label.text = _get_item_info_text(mod_data)
	info_label.horizontal_alignment = HORIZONTAL_ALIGNMENT_CENTER
	info_label.mouse_filter = Control.MOUSE_FILTER_PASS
	info_label.add_theme_font_size_override("font_size", 12)
	
	# 添加标签
	info_container.add_child(name_label)
	info_container.add_child(info_label)
	
	# 组装界面
	container.add_child(icon_container)
	container.add_child(info_container)
	item.add_child(container)
	
	# 添加到物品容器
	mod_inventory_ui.add_child(item)
	
	# 连接选择信号
	item.gui_input.connect(Callable(self, "_on_item_selected").bind(mod_data))

# 当物品被选中
func _on_item_selected(event, mod_data):
	if event is InputEventMouseButton and event.button_index == MOUSE_BUTTON_LEFT and event.pressed:
		emit_signal("mod_item_selected", mod_data)
		print("已选择改装件: " + mod_data.name)

# 获取已装备的改装件ID列表
func _get_equipped_modifications():
	var equipped_mods = []
	
	# 获取玩家角色节点
	var player = get_tree().get_first_node_in_group("player")
	if player and player.has_node("ModificationSystem"):
		var mod_system = player.get_node("ModificationSystem")
		if mod_system.has_method("get_equipped_modifications"):
			equipped_mods = mod_system.get_equipped_modifications()
	
	return equipped_mods

# 获取改装件的尺寸类别
func _get_size_category(mod_data):
	# 获取形状数据
	var shape_data = mod_data.get("shape_data", [[0,0]])
	
	# 根据形状大小分类
	var size = shape_data.size()
	
	if size <= 1:
		return "small"
	elif size <= 5:
		return "medium"
	else:
		return "large"

# 获取改装件的类型类别
func _get_type_category(mod_data):
	# 获取类型数据
	var type = mod_data.get("type", 0)
	
	# 根据类型值分类
	match type:
		1: return "offensive"
		2: return "defensive"
		3: return "special"
		4: return "core"
		_: return "special" # 默认为特殊型
	
	return "special"

# 获取物品信息文本
func _get_item_info_text(mod_data):
	var info_text = ""
	
	# 添加尺寸分类
	var size_category = _get_size_category(mod_data)
	info_text += SIZE_CATEGORIES[size_category]
	
	# 添加类型分类
	var type_category = _get_type_category(mod_data)
	info_text += " | " + TYPE_CATEGORIES[type_category]
	
	# 添加等级信息
	var level = mod_data.get("level", 1)
	info_text += " | Lv." + str(level)
	
	return info_text

# 按名称排序
func _sort_by_name(a, b):
	return a.name < b.name

# 加载测试数据
func _load_test_data():
	if not modification_inventory:
		return
	
	# 添加一些测试改装件到库存
	modification_inventory.add_modification("shield_enhancer", 2)
	modification_inventory.add_modification("health_enhancer", 1)
	modification_inventory.add_modification("speed_enhancer", 3)
	modification_inventory.add_modification("damage_enhancer", 1)
	
	print("已加载测试改装件数据") 
