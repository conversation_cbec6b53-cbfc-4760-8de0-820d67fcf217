list=[{
"base": &"RefCounted",
"class": &"EffectInterface",
"icon": "",
"is_abstract": false,
"is_tool": false,
"language": &"GDScript",
"path": "res://scenes/modifications/effects/interfaces/effect_interface.gd"
}, {
"base": &"Node",
"class": &"ModDataManagerLegacy",
"icon": "",
"is_abstract": false,
"is_tool": false,
"language": &"GDScript",
"path": "res://scenes/modifications/scripts/modification_data_manager.gd"
}, {
"base": &"Resource",
"class": &"ModEffectLegacy",
"icon": "",
"is_abstract": false,
"is_tool": false,
"language": &"GDScript",
"path": "res://scenes/modifications/scripts/modification_effect.gd"
}, {
"base": &"RefCounted",
"class": &"ModGridLegacy",
"icon": "",
"is_abstract": false,
"is_tool": false,
"language": &"GDScript",
"path": "res://scripts/ModificationGrid.gd"
}, {
"base": &"Node",
"class": &"ModInventory",
"icon": "",
"is_abstract": false,
"is_tool": false,
"language": &"GDScript",
"path": "res://scenes/modifications/data/mod_inventory.gd"
}, {
"base": &"Panel",
"class": &"ModInventoryItemEntry",
"icon": "",
"is_abstract": false,
"is_tool": false,
"language": &"GDScript",
"path": "res://scenes/modifications/ui/mod_inventory_item_entry.gd"
}, {
"base": &"Control",
"class": &"ModInventoryPanel",
"icon": "",
"is_abstract": false,
"is_tool": false,
"language": &"GDScript",
"path": "res://scenes/ui/mod_inventory_panel.gd"
}, {
"base": &"Control",
"class": &"ModInventoryUI",
"icon": "",
"is_abstract": false,
"is_tool": false,
"language": &"GDScript",
"path": "res://scenes/modifications/ui/mod_inventory_ui.gd"
}, {
"base": &"Node",
"class": &"ModSystemLegacy",
"icon": "",
"is_abstract": false,
"is_tool": false,
"language": &"GDScript",
"path": "res://scenes/modifications/scripts/modification_system.gd"
}, {
"base": &"Node2D",
"class": &"ModVisual",
"icon": "",
"is_abstract": false,
"is_tool": false,
"language": &"GDScript",
"path": "res://scenes/modifications/visuals/mod_visual.gd"
}, {
"base": &"Node2D",
"class": &"ModVisualComponent",
"icon": "",
"is_abstract": false,
"is_tool": false,
"language": &"GDScript",
"path": "res://scenes/modifications/visuals/modification_visual.gd"
}, {
"base": &"Node2D",
"class": &"ModVisualManager",
"icon": "",
"is_abstract": false,
"is_tool": false,
"language": &"GDScript",
"path": "res://scenes/modifications/visuals/mod_visual_manager.gd"
}, {
"base": &"Node2D",
"class": &"ModVisualsManager",
"icon": "",
"is_abstract": false,
"is_tool": false,
"language": &"GDScript",
"path": "res://scenes/modifications/visuals/modification_visuals_manager.gd"
}, {
"base": &"Node",
"class": &"ModificationDataIO",
"icon": "",
"is_abstract": false,
"is_tool": false,
"language": &"GDScript",
"path": "res://scenes/modifications/data/modification_data_io.gd"
}, {
"base": &"Node",
"class": &"ModificationDataManager",
"icon": "",
"is_abstract": false,
"is_tool": false,
"language": &"GDScript",
"path": "res://scenes/modifications/data/modification_data_manager.gd"
}, {
"base": &"Resource",
"class": &"ModificationEffect",
"icon": "",
"is_abstract": false,
"is_tool": false,
"language": &"GDScript",
"path": "res://scenes/modifications/data/modification_effect.gd"
}, {
"base": &"RefCounted",
"class": &"ModificationGrid",
"icon": "",
"is_abstract": false,
"is_tool": false,
"language": &"GDScript",
"path": "res://scenes/modifications/core/modification_grid.gd"
}, {
"base": &"Resource",
"class": &"ModificationResource",
"icon": "",
"is_abstract": false,
"is_tool": false,
"language": &"GDScript",
"path": "res://scenes/modifications/scripts/modification_resource.gd"
}, {
"base": &"Node",
"class": &"ModificationSystem",
"icon": "",
"is_abstract": false,
"is_tool": false,
"language": &"GDScript",
"path": "res://scenes/modifications/scripts/modification_system_new.gd"
}, {
"base": &"Node",
"class": &"ModificationSystemLegacy",
"icon": "",
"is_abstract": false,
"is_tool": false,
"language": &"GDScript",
"path": "res://scenes/modifications/core/modification_system.gd"
}, {
"base": &"Node2D",
"class": &"ModificationVisualScript",
"icon": "",
"is_abstract": false,
"is_tool": false,
"language": &"GDScript",
"path": "res://scenes/modifications/scripts/modification_visual.gd"
}, {
"base": &"Node2D",
"class": &"ModificationVisualsManager",
"icon": "",
"is_abstract": false,
"is_tool": false,
"language": &"GDScript",
"path": "res://scenes/modifications/scripts/modification_visuals_manager.gd"
}, {
"base": &"Control",
"class": &"NewModInventorySystem",
"icon": "",
"is_abstract": false,
"is_tool": false,
"language": &"GDScript",
"path": "res://scenes/ui/new_mod_inventory_system.gd"
}, {
"base": &"Node2D",
"class": &"NewModVisualSystem",
"icon": "",
"is_abstract": false,
"is_tool": false,
"language": &"GDScript",
"path": "res://scenes/modifications/visuals/new_mod_visual_system.gd"
}, {
"base": &"Control",
"class": &"NewModificationController",
"icon": "",
"is_abstract": false,
"is_tool": false,
"language": &"GDScript",
"path": "res://scenes/ui/new_modification_controller.gd"
}, {
"base": &"Resource",
"class": &"NewModificationData",
"icon": "",
"is_abstract": false,
"is_tool": false,
"language": &"GDScript",
"path": "res://scenes/modifications/data/new_modification_data.gd"
}, {
"base": &"Node",
"class": &"TooltipManager",
"icon": "",
"is_abstract": false,
"is_tool": false,
"language": &"GDScript",
"path": "res://scenes/ui/tooltip_manager.gd"
}]
