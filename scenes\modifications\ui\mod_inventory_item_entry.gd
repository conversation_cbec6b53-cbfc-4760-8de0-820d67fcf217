extends Panel

class_name ModInventoryItemEntry

# 信号
signal mod_selected(mod_resource)

# 改装件资源
var mod_resource = null

# 提示框管理器
var tooltip_manager = null

# 界面组件
var icon_rect = null
var name_label = null
var quantity_label = null
var level_label = null
var type_icon = null
var size_label = null

# 是否被选中
var is_selected = false

# 鼠标悬停样式和常规样式
var hover_style = null
var default_style = null
var selected_style = null

# 添加属性用于存储数量和等级
var quantity: int = 1
var level: int = 1

# 准备
func _ready():
	# 设置鼠标样式
	mouse_default_cursor_shape = Control.CURSOR_POINTING_HAND
	
	# 创建样式
	_setup_styles()
	
	# 创建界面
	_setup_ui()
	
	# 加载提示框管理器
	_load_tooltip_manager()
	
	# 连接鼠标事件
	mouse_entered.connect(_on_mouse_entered)
	mouse_exited.connect(_on_mouse_exited)
	
	# 如果有改装件数据，更新显示
	call_deferred("_update_display_deferred")

# 设置样式
func _setup_styles():
	# 创建默认样式
	default_style = StyleBoxFlat.new()
	default_style.bg_color = Color(0.15, 0.15, 0.15, 0.8)
	default_style.corner_radius_top_left = 5
	default_style.corner_radius_top_right = 5
	default_style.corner_radius_bottom_left = 5
	default_style.corner_radius_bottom_right = 5
	
	# 创建悬停样式
	hover_style = StyleBoxFlat.new()
	hover_style.bg_color = Color(0.25, 0.25, 0.25, 0.9)
	hover_style.corner_radius_top_left = 5
	hover_style.corner_radius_top_right = 5
	hover_style.corner_radius_bottom_left = 5
	hover_style.corner_radius_bottom_right = 5
	
	# 创建选中样式
	selected_style = StyleBoxFlat.new()
	selected_style.bg_color = Color(0.3, 0.5, 0.7, 0.9)
	selected_style.corner_radius_top_left = 5
	selected_style.corner_radius_top_right = 5
	selected_style.corner_radius_bottom_left = 5
	selected_style.corner_radius_bottom_right = 5
	
	# 应用默认样式
	add_theme_stylebox_override("panel", default_style)

# 设置界面
func _setup_ui():
	# 创建主容器
	var main_container = VBoxContainer.new()
	main_container.anchors_preset = Control.PRESET_FULL_RECT
	main_container.offset_right = 0
	main_container.offset_bottom = 0
	main_container.size_flags_horizontal = Control.SIZE_FILL
	main_container.size_flags_vertical = Control.SIZE_FILL
	main_container.mouse_filter = Control.MOUSE_FILTER_PASS
	
	# 创建图标
	icon_rect = ColorRect.new()
	icon_rect.size_flags_horizontal = Control.SIZE_FILL
	icon_rect.size_flags_vertical = Control.SIZE_FILL
	icon_rect.custom_minimum_size = Vector2(80, 80)
	icon_rect.mouse_filter = Control.MOUSE_FILTER_PASS
	
	# 创建图标容器（用于居中图标）
	var icon_container = MarginContainer.new()
	icon_container.size_flags_horizontal = Control.SIZE_FILL
	icon_container.size_flags_vertical = Control.SIZE_SHRINK_CENTER
	icon_container.add_child(icon_rect)
	
	# 创建名称标签
	name_label = Label.new()
	name_label.horizontal_alignment = HORIZONTAL_ALIGNMENT_CENTER
	name_label.add_theme_font_size_override("font_size", 14)
	name_label.text_overrun_behavior = TextServer.OVERRUN_TRIM_ELLIPSIS
	name_label.mouse_filter = Control.MOUSE_FILTER_PASS
	
	# 创建数量标签
	quantity_label = Label.new()
	quantity_label.horizontal_alignment = HORIZONTAL_ALIGNMENT_RIGHT
	quantity_label.add_theme_font_size_override("font_size", 12)
	quantity_label.text = "x1"
	quantity_label.mouse_filter = Control.MOUSE_FILTER_PASS
	
	# 创建等级标签
	level_label = Label.new()
	level_label.horizontal_alignment = HORIZONTAL_ALIGNMENT_LEFT
	level_label.add_theme_font_size_override("font_size", 12)
	level_label.text = "Lv.1"
	level_label.mouse_filter = Control.MOUSE_FILTER_PASS
	
	# 创建等级和数量的水平容器
	var stats_container = HBoxContainer.new()
	stats_container.size_flags_horizontal = Control.SIZE_FILL
	stats_container.mouse_filter = Control.MOUSE_FILTER_PASS
	stats_container.add_child(level_label)
	
	# 添加填充空间，将数量推到右侧
	var spacer = Control.new()
	spacer.size_flags_horizontal = Control.SIZE_EXPAND_FILL
	spacer.mouse_filter = Control.MOUSE_FILTER_PASS
	stats_container.add_child(spacer)
	
	stats_container.add_child(quantity_label)
	
	# 创建尺寸标签 (在图标右上角)
	size_label = Label.new()
	size_label.text = "S"  # 默认小尺寸
	size_label.add_theme_font_size_override("font_size", 16)
	size_label.add_theme_color_override("font_color", Color(1, 1, 1, 0.8))
	size_label.mouse_filter = Control.MOUSE_FILTER_PASS
	
	# 设置尺寸标签位置
	size_label.position = Vector2(icon_rect.position.x + icon_rect.size.x - 20, icon_rect.position.y + 5)
	icon_container.add_child(size_label)
	
	# 将所有元素添加到主容器
	main_container.add_child(icon_container)
	main_container.add_child(name_label)
	main_container.add_child(stats_container)
	
	# 添加主容器到面板
	add_child(main_container)
	
	# 如果有改装件数据，更新显示
	if mod_resource:
		update_display()

# 加载提示框管理器
func _load_tooltip_manager():
	tooltip_manager = load("res://scenes/ui/tooltip_manager.gd").get_instance()

# 更新显示内容
func update_display():
	if not mod_resource:
		print("警告: 试图更新显示，但mod_resource为null")
		return
	
	# 输出调试信息
	print("正在更新物品显示: ", mod_resource.name if mod_resource else "未知")
		
	# 更新图标颜色
	if icon_rect:
		icon_rect.color = mod_resource.color
	else:
		print("警告: icon_rect未初始化")
	
	# 更新名称
	if name_label:
		name_label.text = mod_resource.name
	else:
		print("警告: name_label未初始化")
	
	# 更新数量标签
	if quantity_label:
		quantity_label.text = "x" + str(quantity)
	else:
		print("警告: quantity_label未初始化")
	
	# 更新等级标签
	if level_label:
		level_label.text = "Lv." + str(level)
	else:
		print("警告: level_label未初始化")
	
	# 更新尺寸标签
	if size_label:
		if mod_resource.has_method("has_effect_tag"):
			if mod_resource.has_effect_tag("size_small"):
				size_label.text = "S"
			elif mod_resource.has_effect_tag("size_medium"):
				size_label.text = "M"
			elif mod_resource.has_effect_tag("size_large"):
				size_label.text = "L"
			else:
				size_label.text = "S"  # 默认为小
		else:
			print("警告: mod_resource没有has_effect_tag方法")
			size_label.text = "S"  # 默认为小
	else:
		print("警告: size_label未初始化")

# 鼠标进入事件
func _on_mouse_entered():
	# 应用悬停样式
	if not is_selected:
		add_theme_stylebox_override("panel", hover_style)
	
	# 显示提示框
	if tooltip_manager and mod_resource:
		tooltip_manager.show_tooltip(mod_resource.name, mod_resource.description + "\n\n" + mod_resource.get_effects_description())

# 鼠标离开事件
func _on_mouse_exited():
	# 恢复默认样式（如果未被选中）
	if not is_selected:
		add_theme_stylebox_override("panel", default_style)
	
	# 隐藏提示框
	if tooltip_manager:
		tooltip_manager.hide_tooltip()

# 处理输入事件
func _gui_input(event):
	if event is InputEventMouseButton:
		if event.button_index == MOUSE_BUTTON_LEFT and event.pressed:
			# 左键点击选中
			select()

# 选中此改装件
func select():
	is_selected = true
	add_theme_stylebox_override("panel", selected_style)
	emit_signal("mod_selected", mod_resource)

# 取消选中
func deselect():
	is_selected = false
	add_theme_stylebox_override("panel", default_style)

# 获取拖拽数据
func _get_drag_data(position):
	# 创建拖拽数据
	var drag_data = {
		"type": "modification_part_placeholder",
		"name": mod_resource.name,
		"color": mod_resource.color,
		"id": mod_resource.id,
		"source_type": "new_inventory",  # 标记来源为新物品栏
		"source_item": self  # 引用自身，方便后续处理
	}
	
	# 创建拖拽预览
	var preview = create_drag_preview()
	set_drag_preview(preview)
	
	print("开始从新物品栏拖拽: " + mod_resource.name)
	return drag_data

# 创建拖拽预览
func create_drag_preview():
	# 创建一个面板作为预览容器
	var preview_container = Panel.new()
	preview_container.custom_minimum_size = Vector2(80, 80)
	
	# 复制当前面板样式
	var style = StyleBoxFlat.new()
	style.bg_color = Color(0.2, 0.2, 0.2, 0.8)
	style.corner_radius_top_left = 5
	style.corner_radius_top_right = 5
	style.corner_radius_bottom_left = 5
	style.corner_radius_bottom_right = 5
	preview_container.add_theme_stylebox_override("panel", style)
	
	# 设置半透明效果
	preview_container.modulate = Color(1, 1, 1, 0.7)
	
	# 创建图标
	var icon = ColorRect.new()
	icon.color = mod_resource.color
	icon.size_flags_horizontal = Control.SIZE_EXPAND_FILL
	icon.size_flags_vertical = Control.SIZE_EXPAND_FILL
	icon.custom_minimum_size = Vector2(60, 60)
	
	# 将图标添加到预览容器
	var container = MarginContainer.new()
	container.add_theme_constant_override("margin_left", 10)
	container.add_theme_constant_override("margin_right", 10)
	container.add_theme_constant_override("margin_top", 10)
	container.add_theme_constant_override("margin_bottom", 10)
	container.add_child(icon)
	preview_container.add_child(container)
	
	return preview_container

# 添加延迟更新显示的方法，确保所有UI组件都已就绪
func _update_display_deferred():
	# 等待一帧后再更新显示，确保UI组件都已初始化
	await get_tree().process_frame
	
	if mod_resource:
		update_display()
	else:
		print("警告: 延迟更新显示时，mod_resource仍为null") 